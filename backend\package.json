{"name": "loan-management-backend", "version": "1.0.0", "description": "Backend for Loan Management System", "main": "server-pg.js", "scripts": {"start": "node server-pg.js", "dev": "nodemon server-pg.js", "test": "jest"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "pg": "^8.11.3", "qrcode": "^1.5.3", "sequelize": "^6.35.0", "sqlite3": "^5.1.7"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.1.10"}, "engines": {"node": ">=18.0.0"}}